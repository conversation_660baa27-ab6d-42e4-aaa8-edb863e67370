import asyncio
import os
import logging
import yaml
import hashlib
import openai
import httpx
import base64
import io
import signal
import weakref
from datetime import datetime, timezone
from telethon import TelegramClient, events
from telethon.tl.types import Channel, Chat, User, PeerChannel, PeerChat, PeerUser
from telethon.tl.functions.channels import GetFullChannelRequest
from dotenv import load_dotenv
from supabase import create_client
from typing import Dict, Any, List, Optional, Set
from openai import OpenAI

# 配置日志,如果需要调试
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
    # level=logging.DEBUG
)
logger = logging.getLogger(__name__)


# 加载环境变量
load_dotenv()

# API凭据配置
API_ID = int(os.getenv('API_ID'))
API_HASH = os.getenv('API_HASH')
PHONE_NUMBER = os.getenv('PHONE_NUMBER')
SESSION_NAME = 'monitor_session'

# 历史消息获取配置
FETCH_HISTORY_COUNT = int(os.getenv('FETCH_HISTORY_COUNT', '0'))

# Supabase配置
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# 初始化Supabase客户端，添加错误处理
supabase = None
if SUPABASE_URL and SUPABASE_KEY:
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        logger.info(f"Supabase客户端初始化成功: {SUPABASE_URL}")
    except Exception as e:
        logger.error(f"Supabase客户端初始化失败: {e}")
        # 尝试使用httpx直接连接
        try:
            import httpx
            # 测试连接
            response = httpx.get(f"{SUPABASE_URL}/rest/v1/?apikey={SUPABASE_KEY}")
            logger.info(f"Supabase连接测试: {response.status_code}")
        except Exception as http_e:
            logger.error(f"Supabase连接测试失败: {http_e}")

# OpenAI配置
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
client = openai.OpenAI(api_key=OPENAI_API_KEY)

# 向量缓存，用于相似度计算
vector_cache = []

# YAML配置文件路径
CONFIG_FILE = 'monitor_config.yaml'

# 默认配置
DEFAULT_CONFIG = {
    'channels': [],  # 频道用户名或ID列表
    'bots': []       # 机器人用户名列表
}


# 消息缓存，用于避免重复处理
message_cache = {}

# 过滤消息统计
filtered_message_count = 0

# 并发控制
import asyncio
MAX_CONCURRENT_MESSAGES = int(os.getenv('MAX_CONCURRENT_MESSAGES', '10'))  # 最大并发处理消息数
message_semaphore = asyncio.Semaphore(MAX_CONCURRENT_MESSAGES)

# 任务管理 - 用于跟踪所有异步任务，确保优雅退出
active_tasks: Set[asyncio.Task] = set()
shutdown_event = asyncio.Event()

def add_task(task: asyncio.Task) -> None:
    """添加任务到活跃任务集合"""
    active_tasks.add(task)
    # 使用弱引用回调，任务完成时自动从集合中移除
    task.add_done_callback(lambda t: active_tasks.discard(t))

async def graceful_shutdown(client: TelegramClient = None) -> None:
    """优雅关闭程序，清理所有资源"""
    logger.info("开始优雅关闭程序...")

    # 设置关闭事件
    shutdown_event.set()

    # 取消所有活跃任务
    if active_tasks:
        logger.info(f"正在取消 {len(active_tasks)} 个活跃任务...")
        for task in active_tasks.copy():
            if not task.done():
                task.cancel()

        # 等待任务完成或取消，最多等待5秒
        try:
            await asyncio.wait_for(
                asyncio.gather(*active_tasks, return_exceptions=True),
                timeout=5.0
            )
        except asyncio.TimeoutError:
            logger.warning("部分任务未能在5秒内完成，强制退出")
        except Exception as e:
            logger.debug(f"任务清理过程中的异常（可忽略）: {e}")

    # 关闭Telegram客户端
    if client:
        try:
            logger.info("正在断开Telegram连接...")
            await asyncio.wait_for(client.disconnect(), timeout=3.0)
            logger.info("Telegram连接已断开")
        except asyncio.TimeoutError:
            logger.warning("Telegram断开连接超时")
        except Exception as e:
            logger.debug(f"断开Telegram连接时的异常（可忽略）: {e}")

    logger.info("程序已优雅关闭")

# 初始化 OpenRouter 和 OpenAI 客户端
openrouter_client = OpenAI(
    api_key=os.getenv('OPENROUTER_API_KEY'),
    base_url="https://openrouter.ai/api/v1"
)

openai_client = OpenAI(
    api_key=os.getenv('OPENAI_API_KEY'),
    base_url=os.getenv('OPENAI_API_BASE', "https://api.openai.com/v1")
)

async def get_image_base64_from_memory(message) -> Optional[str]:
    """直接在内存中处理图片，避免磁盘I/O，用于AI视觉分析"""
    try:
        if not hasattr(message, 'photo') or not message.photo:
            logger.debug("消息不包含图片")
            return None

        # 下载图片到内存
        image_bytes = io.BytesIO()
        await message.download_media(image_bytes)

        # 检查图片大小
        image_bytes.seek(0, 2)  # 移动到文件末尾
        image_size = image_bytes.tell()
        image_bytes.seek(0)  # 重置到开头

        max_size = 20 * 1024 * 1024  # 20MB限制
        if image_size > max_size:
            logger.warning(f"图片过大 ({image_size / 1024 / 1024:.2f}MB > 20MB)，跳过AI处理")
            return None

        # 读取图片数据并编码为base64
        image_data = image_bytes.read()
        if not image_data:
            logger.warning("图片数据为空")
            return None

        encoded_string = base64.b64encode(image_data).decode('utf-8')
        logger.debug(f"图片内存编码成功，大小: {image_size / 1024:.2f}KB")
        return encoded_string

    except Exception as e:
        logger.error(f"内存处理图片时出错: {e}")
        return None



async def get_ai_response(text_content: str, image_base64: Optional[str] = None) -> Dict[str, str]:
    """使用OpenRouter API处理文本内容和图片，生成摘要、标签和symbol（异步版本）"""
    try:
        # 计算内容的哈希值，用于缓存（包含文本和图片）
        content_for_hash = text_content
        if image_base64:
            # 如果有图片，将图片的哈希值加入缓存计算
            image_hash = hashlib.md5(image_base64.encode('utf-8')).hexdigest()
            content_for_hash += f"_img_{image_hash}"

        content_hash = hashlib.md5(content_for_hash.encode('utf-8')).hexdigest()

        # 检查缓存
        if content_hash in message_cache:
            logger.debug(f"从缓存获取AI处理结果: {content_hash[:8]}...")
            return message_cache[content_hash]

        if not os.getenv('OPENROUTER_API_KEY'):
            logger.warning("未配置OpenRouter API密钥，无法进行AI处理")
            return {"总结": "AI处理失败", "标签": "#处理失败", "symbol": "BTCUSDC"}

        # 检查是否有图片数据
        has_image = bool(image_base64)

        if has_image and text_content:
            logger.info(f"开始进行AI处理（文本+图片）: {text_content[:50]}...")
        elif has_image and not text_content:
            logger.info(f"开始进行AI处理（仅图片）")
        else:
            logger.info(f"开始进行AI处理（仅文本）: {text_content[:50]}...")

        start_time = datetime.now(timezone.utc)

        # 构建提示，根据是否有图片和文本调整
        if has_image and text_content:
            # 有图片也有文本
            prompt = f"""请对以下相关文本和图片进行综合分析,润色,总结,标签,交易对symbol,请严格遵守以下规则:
            文本内容: {text_content}
            图片: 请分析图片中的内容，包括文字、图表、数据等信息，并与文本内容结合进行分析。

            请严格按以下格式回复,回复内容不能包含MONITOR_CHANNELS参数,不能包含MONITOR_CHANNELS参数对应的频道名称,不要包含任何额外说明:
            总结:
            [此处是中文总结概述,最多不超过120个字,包含核心要点,保留原文90%以上的本意,只能适当润色,可以包含原文中的关键数据，例如交易对，具体的symbol，具体的时间,一些英文的公司,名词,项目名称等。如果图片包含重要信息，请一并总结。]

            标签:
            [仅输出标签，用空格分隔，每个标签都要带#号：
            1. 第一个标签必须是 #利好、#利空 或 #中性 中的一个，用来表示这条消息对加密货币市场的影响。
            2. 如果提到具体的加密货币，添加对应标签如 #BTC #ETH
            3. 补充1-2个主题相关标签
            4. 标签一共输出3个或者4个，所有标签累计中文不超过8个汉字，英文不超过8个字符（不含#号）
            ]

            symbol:
            [必须返回一个加密货币交易对。如果文本或图片中提到了具体的加密货币交易对，请输出最相关的一个交易对，格式为"BTCUSDT"、"ETHUSDT"等。如果没有明确提到交易对，但提到了某个加密货币，则输出该币种与USDT的交易对，如"BTCUSDT"。如果完全没有提到任何加密货币，则输出"BTCUSDC"。不要输出"NONE"或空值。]
            """
        elif has_image and not text_content:
            # 只有图片，没有文本
            prompt = f"""请对以下图片进行分析,总结,标签,交易对symbol,请严格遵守以下规则:
            图片: 请分析图片中的内容，包括文字、图表、数据等信息，提取关键信息。

            请严格按以下格式回复,回复内容不能包含MONITOR_CHANNELS参数,不能包含MONITOR_CHANNELS参数对应的频道名称,不要包含任何额外说明:
            总结:
            [此处是中文总结概述,最多不超过120个字,包含核心要点,描述图片中的关键信息，可以包含图片中的关键数据，例如交易对，具体的symbol，具体的时间,一些英文的公司,名词,项目名称等。]

            标签:
            [仅输出标签，用空格分隔，每个标签都要带#号：
            1. 第一个标签必须是 #利好、#利空 或 #中性 中的一个，用来表示这条消息对加密货币市场的影响。
            2. 如果提到具体的加密货币，添加对应标签如 #BTC #ETH
            3. 补充1-2个主题相关标签
            4. 标签一共输出3个或者4个，所有标签累计中文不超过8个汉字，英文不超过8个字符（不含#号）
            ]

            symbol:
            [必须返回一个加密货币交易对。如果图片中提到了具体的加密货币交易对，请输出最相关的一个交易对，格式为"BTCUSDT"、"ETHUSDT"等。如果没有明确提到交易对，但提到了某个加密货币，则输出该币种与USDT的交易对，如"BTCUSDT"。如果完全没有提到任何加密货币，则输出"BTCUSDC"。不要输出"NONE"或空值。]
            """
        else:
            prompt = f"""请对以下相关文本进行分析,润色,总结,标签,交易对symbol,请严格遵守以下规则:
            原文: {text_content}

            请严格按以下格式回复,回复内容不能包含MONITOR_CHANNELS参数,不能包含MONITOR_CHANNELS参数对应的频道名称,不要包含任何额外说明:
            总结:
            [此处是中文总结概述,最多不超过120个字,包含核心要点,保留原文90%以上的本意,只能适当润色,可以包含原文中的关键数据，例如交易对，具体的symbol，具体的时间,一些英文的公司,名词,项目名称等。]

            标签:
            [仅输出标签，用空格分隔，每个标签都要带#号：
            1. 第一个标签必须是 #利好、#利空 或 #中性 中的一个，用来表示这条消息对加密货币市场的影响。
            2. 如果提到具体的加密货币，添加对应标签如 #BTC #ETH
            3. 补充1-2个主题相关标签
            4. 标签一共输出3个或者4个，所有标签累计中文不超过8个汉字，英文不超过8个字符（不含#号）
            ]

            symbol:
            [必须返回一个加密货币交易对。如果文本中提到了具体的加密货币交易对，请输出最相关的一个交易对，格式为"BTCUSDT"、"ETHUSDT"等。如果没有明确提到交易对，但提到了某个加密货币，则输出该币种与USDT的交易对，如"BTCUSDT"。如果完全没有提到任何加密货币，则输出"BTCUSDC"。不要输出"NONE"或空值。]
            """

        # 构建消息内容
        if has_image:
            # 包含图片的消息格式
            messages = [
                {"role": "system", "content": "你擅长提取文本和图片的核心和关键信息,不要进行过多的分析和添加内容,无法进行实质性分析的时候，回复___符号即可,请用中文回复。"},
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]
        else:
            # 仅文本的消息格式
            messages = [
                {"role": "system", "content": "你擅长提取文本核心和关键信息,不要进行过多的分析和添加内容,无法进行实质性分析的时候，回复___符号即可,请用中文回复。"},
                {"role": "user", "content": prompt}
            ]

        # 使用OpenRouter API进行处理（异步调用）
        loop = asyncio.get_event_loop()
        response = await loop.run_in_executor(
            None,
            lambda: openrouter_client.chat.completions.create(
                model=os.getenv('OPENROUTER_MODEL', 'anthropic/claude-3.5-haiku'),
                messages=messages,
                temperature=0.9
            )
        )

        # 解析响应
        ai_response_text = response.choices[0].message.content

        # 解析返回内容
        总结 = ""
        标签 = ""
        symbol = "BTCUSDC"  # 默认值

        parts = ai_response_text.split("标签:")
        if len(parts) >= 2:
            总结_part = parts[0].split("总结:")
            if len(总结_part) >= 2:
                总结 = 总结_part[1].strip()

            # 处理标签和symbol
            remaining = parts[1]
            symbol_parts = remaining.split("symbol:")

            if len(symbol_parts) >= 2:
                标签 = symbol_parts[0].strip()
                symbol = symbol_parts[1].strip()
            else:
                标签 = remaining.strip()

        # 使用默认值处理不完整的结果
        if not 总结:
            总结 = "内容摘要生成失败"
        if not 标签:
            标签 = "#中性 #处理失败"

        # 确保标签格式正确
        if not 标签.startswith('#'):
            标签 = '#中性 ' + 标签  # 默认添加中性标签

        # 清理和验证symbol值
        symbol = symbol.strip().upper()
        if not symbol or symbol == "" or symbol == "NONE":
            symbol = "BTCUSDC"

        result = {
            "总结": 总结,
            "标签": 标签,
            "symbol": symbol
        }

        # 保存到缓存
        message_cache[content_hash] = result

        # 计算并记录处理时间
        end_time = datetime.now(timezone.utc)
        processing_time = (end_time - start_time).total_seconds()
        logger.info(f"AI处理完成，耗时: {processing_time:.2f}秒")

        return result

    except Exception as e:
        logger.error(f"AI处理失败: {e}")
        # 返回默认值
        return {
            "总结": "AI处理失败，但保留消息记录",
            "标签": "#中性 #处理失败",
            "symbol": "BTCUSDC"
        }

def get_embedding(text: str) -> List[float]:
    """使用OpenAI API获取文本的向量表示"""
    try:
        if not os.getenv('OPENAI_API_KEY'):
            return None

        # 使用OpenAI API进行向量计算
        response = openai_client.embeddings.create(
            model=os.getenv('OPENAI_MODEL', 'text-embedding-3-small'),
            input=text,
            timeout=30  # 增加超时时间到30秒
        )
        return response.data[0].embedding
    except Exception as e:
        logger.error(f"获取文本向量失败: {e}")
        return None

def calculate_similarity(vec1: List[float], vec2: List[float]) -> float:
    """计算两个向量的余弦相似度，增加错误处理"""
    try:
        if not vec1 or not vec2:
            return 0.0

        if len(vec1) != len(vec2):
            logger.error(f"向量维度不匹配: {len(vec1)} vs {len(vec2)}")
            return 0.0

        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        norm1 = sum(a * a for a in vec1) ** 0.5
        norm2 = sum(b * b for b in vec2) ** 0.5

        if norm1 * norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)
    except Exception as e:
        logger.error(f"计算相似度失败: {e}")
        return 0.0

def process_semantic_similarity(summary: str) -> Dict[str, Any]:
    """处理语义相似度并返回结果，增加错误处理"""
    try:
        # 如果文本为空，直接返回
        if not summary or len(summary.strip()) == 0:
            return {"similarity_score": 0.0, "vector": None}

        # 获取当前消息的向量
        try:
            current_vector = get_embedding(summary)
        except Exception as e:
            logger.error(f"获取向量失败，跳过相似度计算: {e}")
            return {"similarity_score": 0.0, "vector": None}

        if not current_vector:
            return {"similarity_score": 0.0, "vector": None}

        # 计算与历史消息的相似度
        max_similarity = 0.0
        valid_vectors = [v for v in vector_cache if v is not None]

        for cached_vector in valid_vectors:
            try:
                similarity = calculate_similarity(current_vector, cached_vector)
                max_similarity = max(max_similarity, similarity)
            except Exception as e:
                logger.error(f"计算相似度时出错: {e}")
                continue

        # 更新向量缓存
        if current_vector is not None:
            vector_cache.append(current_vector)
            # 维护缓存大小
            while len(vector_cache) > 1000:  # 最多保存最近1000条消息的向量
                vector_cache.pop(0)

        return {
            "similarity_score": max_similarity,
            "vector": current_vector
        }
    except Exception as e:
        logger.error(f"处理语义相似度失败: {e}")
        return {"similarity_score": 0.0, "vector": None}

async def save_to_supabase(message_data: Dict[str, Any]) -> bool:
    """将消息数据保存到Supabase"""
    try:
        if not supabase:
            logger.warning("未配置Supabase，无法保存数据")
            return False

        # 检查必要字段
        if not message_data.get("message_link"):
            logger.warning("消息链接为空，无法保存数据")
            return False

        # 移除不在数据库中的字段
        # 确保只保留数据库中存在的字段
        valid_fields = [
            "channel_name", "message_link", "content", "summary",
            "tags", "symbol", "similarity_score", "message_type",
            "original_send_time", "receive_time", "process_time", "media_info"
        ]

        # 创建一个新的字典，只包含有效字段
        filtered_data = {k: v for k, v in message_data.items() if k in valid_fields}

        # 调整 similarity_score 精度，保留两位小数
        if "similarity_score" in filtered_data:
            try:
                filtered_data["similarity_score"] = round(float(filtered_data["similarity_score"]), 2)
            except (ValueError, TypeError) as e:
                logger.warning(f"调整 similarity_score 精度失败: {e}")

        # 不再显式设置 created_at 字段，使用 Supabase 默认的 UTC+0 时区

        # 检查是否已存在相同的消息链接
        try:
            existing = supabase.table('telegram_messages').select('id').eq('message_link', filtered_data['message_link']).execute()
            if existing.data and len(existing.data) > 0:
                logger.info(f"消息已存在，跳过保存: {filtered_data['message_link']}")
                return True
        except Exception as check_error:
            logger.warning(f"检查消息是否存在时出错: {check_error}")

        # 插入数据
        result = supabase.table('telegram_messages').insert(filtered_data).execute()

        if result.data:
            logger.info(f"数据已成功保存到Supabase，ID: {result.data[0]['id']}")
            return True
        else:
            # 检查错误类型
            error = getattr(result, 'error', None)
            if error and isinstance(error, dict) and error.get('code') == '23505':
                # 唯一约束冲突，消息已存在
                logger.info(f"消息已存在，跳过保存: {filtered_data['message_link']}")
                return True
            else:
                logger.error(f"保存数据失败: {error}")
                return False
    except Exception as e:
        error_str = str(e)
        # 检查是否是唯一约束冲突错误
        if '23505' in error_str and 'unique_message_link' in error_str:
            logger.info(f"消息已存在，跳过保存: {message_data.get('message_link', 'unknown')}")
            return True
        else:
            logger.error(f"保存数据到Supabase时出错: {e}")
            return False

def load_config():
    """加载YAML配置文件，如果不存在则创建默认配置"""
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
                logger.info(f"已加载配置文件: {CONFIG_FILE}")

                # 确保配置中的列表字段不是None
                if config.get('channels') is None:
                    config['channels'] = []
                if config.get('bots') is None:
                    config['bots'] = []

                return config
        else:
            # 创建默认配置文件
            with open(CONFIG_FILE, 'w', encoding='utf-8') as file:
                yaml.dump(DEFAULT_CONFIG, file, default_flow_style=False, allow_unicode=True)
            logger.info(f"已创建默认配置文件: {CONFIG_FILE}")
            return DEFAULT_CONFIG
    except PermissionError:
        logger.error(f"权限不足，无法访问配置文件: {CONFIG_FILE}")
        return DEFAULT_CONFIG
    except Exception as e:
        logger.error(f"加载配置文件出错: {e}")
        return DEFAULT_CONFIG

async def process_message(client, event, is_history=False, **kwargs):
    """处理消息（新消息或历史消息）并保存到Supabase"""
    # 使用信号量控制并发数量
    async with message_semaphore:
        try:
            # 记录首次接收时间，使用 UTC+0 时区
            receive_time = datetime.now(timezone.utc)

            # 获取消息来源
            chat = await event.get_chat()

            # 如果是频道但没有用户名，尝试获取完整信息
            if isinstance(chat, Channel) and not chat.username:
                try:
                    # 使用GetFullChannelRequest获取完整信息
                    full_channel = await client(GetFullChannelRequest(chat))
                    # 检查完整信息中是否有用户名
                    if hasattr(full_channel, 'chats') and full_channel.chats:
                        for full_chat in full_channel.chats:
                            if full_chat.id == chat.id and hasattr(full_chat, 'username') and full_chat.username:
                                chat = full_chat
                                logger.info(f"成功获取频道 {chat.id} 的用户名: {chat.username}")
                                break
                except Exception as e:
                    logger.warning(f"获取频道完整信息失败: {e}")

            sender = await event.get_sender()

            # 确定实体类型
            entity_type = "未知"
            if isinstance(chat, Channel):
                if chat.broadcast:
                    entity_type = "频道"
                else:
                    entity_type = "超级群组"
            elif isinstance(chat, Chat):
                entity_type = "群组"
            elif isinstance(sender, User) and sender.bot:
                entity_type = "机器人"
            elif isinstance(sender, User):
                entity_type = "用户"

            # 获取消息对象 - 统一处理历史消息和新消息
            # 对于历史消息，event本身就是消息对象
            # 对于新消息，event.message是消息对象
            if hasattr(event, 'message') and event.message:
                message = event.message
            else:
                message = event  # 历史消息的情况

            # 记录消息对象类型，帮助调试
            logger.debug(f"消息对象类型: {type(message)}")

            # 如果是历史消息，只在调试级别输出信息
            if is_history and logger.isEnabledFor(logging.DEBUG):
                # 输出消息的所有属性，帮助调试
                if hasattr(message, '__dict__'):
                    logger.debug(f"消息属性: {message.__dict__}")

            # 获取消息文本，尝试多种可能的属性
            message_text = "[无文本内容]"

            # 历史消息和实时消息的处理方式可能不同
            if is_history:
                # 历史消息处理
                if hasattr(message, 'message') and message.message:
                    message_text = message.message
                elif hasattr(message, 'text') and message.text:
                    message_text = message.text
                elif hasattr(message, 'raw_text') and message.raw_text:
                    message_text = message.raw_text
                # 如果仍然没有获取到文本，尝试获取消息内容
                elif hasattr(message, 'content') and message.content:
                    if hasattr(message.content, 'text'):
                        message_text = message.content.text
                    elif hasattr(message.content, 'caption') and message.content.caption:
                        message_text = message.content.caption.text

                # 如果仍然没有获取到文本，尝试直接将消息转换为字符串
                if message_text == "[无文本内容]" and str(message) != str(type(message)):
                    message_text = str(message)
            else:
                # 实时消息处理
                if hasattr(message, 'message') and message.message:
                    message_text = message.message
                elif hasattr(message, 'text') and message.text:
                    message_text = message.text
                elif hasattr(message, 'raw_text') and message.raw_text:
                    message_text = message.raw_text

            # 获取消息日期和ID
            # 只从消息中获取日期，如果无法获取则使用接收时间
            message_date = message.date if hasattr(message, 'date') else None
            if message_date is None:
                logger.warning("无法从消息中获取日期信息，将使用接收时间")
                message_date = receive_time  # 使用接收时间作为备选
            message_id = None

            # 如果是历史消息，并且传递了原始ID，直接使用
            if is_history and 'original_message_id' in kwargs:
                message_id = kwargs['original_message_id']
                logger.info(f"使用传递的历史消息原始ID: {message_id}")
            # 否则尝试从消息对象获取ID
            elif hasattr(message, 'id') and message.id:
                message_id = message.id
                logger.info(f"获取到消息ID: {message_id}")
            elif hasattr(message, 'message_id') and message.message_id:
                message_id = message.message_id
                logger.info(f"从message_id属性获取到消息ID: {message_id}")
            elif hasattr(message, 'reply_to_msg_id') and message.reply_to_msg_id:
                message_id = message.reply_to_msg_id
                logger.info(f"从reply_to_msg_id属性获取到消息ID: {message_id}")

            # 如果仍然没有找到ID，尝试从其他属性获取
            if message_id is None:
                try:
                    for attr_name in dir(message):
                        if 'id' in attr_name.lower() and not attr_name.startswith('_'):
                            attr_value = getattr(message, attr_name)
                            if isinstance(attr_value, int) and attr_value > 0:
                                message_id = attr_value
                                logger.info(f"从{attr_name}属性获取到消息ID: {message_id}")
                                break
                except Exception as e:
                    logger.warning(f"尝试获取消息ID时出错: {e}")

            # 如果仍然没有找到ID，记录警告并继续处理
            if message_id is None:
                message_id = 0
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug("未能找到有效的消息ID")

            # 处理图片
            has_photo = False
            media_info = None
            image_base64 = None

            # 只有当消息不是字符串类型时才处理图片
            if not isinstance(message, str) and hasattr(message, 'photo') and message.photo:
                has_photo = True

                # 使用内存处理进行AI分析
                logger.debug("开始内存处理图片用于AI分析...")
                image_base64 = await get_image_base64_from_memory(message)

                if image_base64:
                    logger.debug("图片内存处理成功，准备发送给AI")
                else:
                    logger.warning("图片内存处理失败，将跳过AI图片分析")

                # 创建媒体信息
                if hasattr(message.photo, 'sizes'):
                    sizes = message.photo.sizes
                    if sizes:
                        max_size = max(sizes, key=lambda x: getattr(x, 'w', 0) * getattr(x, 'h', 0))
                        media_info = {
                            "宽度": getattr(max_size, 'w', 0),
                            "高度": getattr(max_size, 'h', 0),
                            "文件大小": f"{getattr(max_size, 'size', 0) / 1024:.2f}KB"
                        }

            # 早期过滤：如果既没有文本内容也没有图片，直接跳过处理
            if message_text == "[无文本内容]" and not has_photo:
                global filtered_message_count
                filtered_message_count += 1
                logger.info(f"消息无文本内容且无图片，跳过处理 {'[历史消息]' if is_history else '[实时消息]'}: {chat.id}/{message_id} (累计过滤: {filtered_message_count})")
                return

            # 获取频道链接
            chat_link = ""
            message_link = ""

            # 尝试创建消息链接，即使消息ID为0也创建一个基本链接
            # 优先使用用户名构建链接
            if hasattr(chat, 'username') and chat.username:
                chat_link = f" (t.me/{chat.username})"

                # 如果消息ID大于0，创建完整链接
                if message_id > 0:
                    message_link = f"https://t.me/{chat.username}/{message_id}"
                    logger.info(f"消息ID: {message_id}, 使用用户名构建消息链接: {message_link}")
                else:
                    # 即使ID为0，也创建一个频道链接
                    message_link = f"https://t.me/{chat.username}"
                    logger.debug(f"使用频道链接: {message_link}")

            # 如果没有用户名，则使用ID构建链接
            elif hasattr(chat, 'id'):
                # Telegram 公开频道/群组链接格式：https://t.me/c/channel_id/message_id
                # 注意：Telegram API 返回的 ID 需要去掉前面的 -100
                chat_id = str(chat.id)
                if chat_id.startswith('-100'):
                    chat_id = chat_id[4:]  # 移除 -100 前缀

                chat_link = f" (t.me/c/{chat_id})"

                # 如果消息ID大于0，创建完整链接
                if message_id > 0:
                    message_link = f"https://t.me/c/{chat_id}/{message_id}"
                    logger.info(f"消息ID: {message_id}, 使用ID构建消息链接: {message_link}")
                else:
                    # 即使ID为0，也创建一个频道链接
                    message_link = f"https://t.me/c/{chat_id}"
                    logger.debug(f"使用频道ID链接: {message_link}")
            else:
                logger.warning("无法获取频道用户名或ID，无法创建有效的消息链接")

            # 标记是否为历史消息
            history_tag = "[历史消息] " if is_history else ""

            # 输出消息信息
            logger.info(f"{history_tag}时间: {message_date}")
            logger.info(f"{history_tag}实体类型: {entity_type}")
            logger.info(f"{history_tag}来源: {getattr(chat, 'title', None) or getattr(chat, 'username', '未知')}{chat_link}")
            logger.info(f"{history_tag}来源ID: {chat.id}")
            logger.info(f"{history_tag}发送者: {getattr(sender, 'first_name', None) or getattr(sender, 'username', '未知')}")
            logger.info(f"{history_tag}消息: {message_text}")
            if has_photo:
                logger.info(f"{history_tag}包含图片，已进行内存处理")
            logger.info("\n" + "📌------------" + "\n"*3)

            # 初始化AI处理相关变量
            ai_start_time = None
            ai_response = {"总结": "无文本内容，跳过AI处理", "标签": "#无文本", "symbol": "BTCUSDC"}
            ai_process_time = 0

            # 如果消息有文本内容或有图片，进行AI处理
            if (message_text and message_text != "[无文本内容]") or has_photo:
                # 设置AI处理开始时间
                ai_start_time = datetime.now(timezone.utc)

                # 根据内容类型记录不同的日志
                if message_text and message_text != "[无文本内容]" and has_photo:
                    logger.info(f"成功获取到消息文本和图片: {message_text[:50]}...")
                    logger.info(f"开始AI处理（文本+图片） {'[历史消息]' if is_history else '[实时消息]'}: {chat.id}/{message_id}")
                elif message_text and message_text != "[无文本内容]":
                    logger.info(f"成功获取到消息文本: {message_text[:50]}...")
                    logger.info(f"开始AI处理（仅文本） {'[历史消息]' if is_history else '[实时消息]'}: {chat.id}/{message_id}")
                elif has_photo:
                    logger.info(f"消息仅包含图片，无文本内容")
                    logger.info(f"开始AI处理（仅图片） {'[历史消息]' if is_history else '[实时消息]'}: {chat.id}/{message_id}")

                # AI处理（异步）
                # 对于仅有图片的消息，传递空字符串作为文本内容
                text_for_ai = message_text if message_text != "[无文本内容]" else ""
                ai_response = await get_ai_response(text_for_ai, image_base64)

                # 记录AI处理耗时
                ai_process_time = (datetime.now(timezone.utc) - ai_start_time).total_seconds()
                logger.info(f"AI处理完成，耗时: {ai_process_time:.2f}秒 {'[历史消息]' if is_history else '[实时消息]'}")
            else:
                logger.info(f"消息无有效文本内容且无图片，跳过AI处理 {'[历史消息]' if is_history else '[实时消息]'}")

            # 计算语义相似度
            semantic_result = process_semantic_similarity(ai_response.get("总结", ""))
            similarity_score = semantic_result["similarity_score"]

            # 检查相似度是否超过阈值
            similarity_threshold = float(os.getenv('SIMILARITY_THRESHOLD', '0.85'))
            if similarity_score > similarity_threshold:
                logger.info(f"检测到高相似度消息 ({similarity_score:.2f} > {similarity_threshold})")

            # 从标签中提取加密货币符号
            tags = ai_response.get("标签", "#处理失败")
            symbol = ai_response.get("symbol", "BTCUSDC")

            # 处理完成时间
            complete_time = datetime.now(timezone.utc)

            # 计算处理时间（秒），保留3位小数
            process_time_seconds = (complete_time - receive_time).total_seconds()
            process_time_seconds = round(process_time_seconds, 3)

            # 构建要保存到Supabase的数据
            message_data = {
                "channel_name": getattr(chat, 'title', None) or getattr(chat, 'username', '未知'),
                "message_link": message_link,
                "content": message_text,
                "summary": ai_response.get("总结", "处理失败"),
                "tags": tags,
                "symbol": symbol,
                "similarity_score": similarity_score,
                "message_type": "图片+文本" if has_photo else "文本",
                "original_send_time": message_date.strftime('%Y-%m-%d %H:%M:%S') if hasattr(message_date, 'strftime') else str(message_date),
                "receive_time": receive_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                "process_time": process_time_seconds,
                "media_info": media_info
            }

            # 如果消息ID为0，记录警告但不修改链接
            if message_id == 0:
                logger.warning("消息ID为0，可能无法正确构建消息链接")

            # 输出处理结果
            logger.info(f"\n处理结果:\n总结: {ai_response.get('总结', '处理失败')}\n标签: {tags}\n相似度: {similarity_score:.2f}")

            # 确保AI处理结果正确生成后再保存到Supabase
            if ai_response.get("总结", "") and ai_response.get("标签", ""):
                # 只有在有有效的消息链接时才保存到Supabase
                if message_link:
                    # 记录保存到Supabase的开始时间
                    save_start_time = datetime.now(timezone.utc)
                    save_result = await save_to_supabase(message_data)
                    save_time = (datetime.now(timezone.utc) - save_start_time).total_seconds()

                    if save_result:
                        logger.info(f"数据保存到Supabase成功，耗时: {save_time:.2f}秒 {'[历史消息]' if is_history else '[实时消息]'}")
                    else:
                        logger.warning(f"数据保存到Supabase失败，耗时: {save_time:.2f}秒 {'[历史消息]' if is_history else '[实时消息]'}")
                else:
                    logger.warning("消息链接为空，跳过保存到Supabase")
            else:
                logger.warning(f"AI处理结果不完整，跳过保存到Supabase: {ai_response}")

        except Exception as e:
            logger.error(f"处理消息时出错: {e}", exc_info=True)

async def resolve_entity(client, entity_id):
    """解析实体ID或用户名为Telegram实体对象"""
    try:
        # 检查是否是数字ID
        if isinstance(entity_id, int) or (isinstance(entity_id, str) and entity_id.isdigit()):
            # 转换为整数ID
            entity_id = int(entity_id)

            # 使用ID解析
            try:
                # 尝试作为频道ID
                entity = await client.get_entity(PeerChannel(entity_id))

                # 如果是频道但没有用户名，尝试获取完整信息
                if isinstance(entity, Channel) and not entity.username:
                    try:
                        # 使用GetFullChannelRequest获取完整信息
                        full_channel = await client(GetFullChannelRequest(entity))
                        # 更新实体信息
                        if hasattr(full_channel, 'chats') and full_channel.chats:
                            for chat in full_channel.chats:
                                if chat.id == entity.id and hasattr(chat, 'username') and chat.username:
                                    entity = chat
                                    break
                    except Exception as e:
                        logger.warning(f"获取频道完整信息失败: {e}")

                return entity
            except Exception as e:
                logger.debug(f"尝试作为频道ID失败: {e}")
                try:
                    # 尝试作为群组ID
                    return await client.get_entity(PeerChat(entity_id))
                except Exception as e:
                    logger.debug(f"尝试作为群组ID失败: {e}")
                    try:
                        # 尝试作为用户ID
                        return await client.get_entity(PeerUser(entity_id))
                    except Exception as e:
                        logger.error(f"无法解析ID: {entity_id}，错误: {e}")
                        return None
        else:
            # 如果是用户名，确保没有@前缀
            clean_username = entity_id.lstrip('@')

            try:
                # 直接使用用户名获取实体
                return await client.get_entity(clean_username)
            except Exception as e:
                logger.error(f"无法解析用户名: {clean_username}，错误: {e}")
                return None
    except Exception as e:
        logger.error(f"解析实体出错: {e}")
        return None

def log_system_info():
    """记录基本系统信息"""
    logger.info("=== 系统信息 ===")
    logger.info(f"配置文件: {CONFIG_FILE}")
    logger.info(f"内存处理模式: 已启用")
    logger.info("===============")

async def main():
    # 记录系统信息
    log_system_info()

    # 加载配置
    config = load_config()

    # 初始化客户端
    client = TelegramClient(SESSION_NAME, API_ID, API_HASH)

    # 设置信号处理器
    def signal_handler(signum, _):
        logger.info(f"接收到信号 {signum}，开始关闭程序...")
        # 创建关闭任务
        asyncio.create_task(graceful_shutdown(client))

    # 注册信号处理器（仅在Unix系统上）
    if hasattr(signal, 'SIGTERM'):
        signal.signal(signal.SIGTERM, signal_handler)
    if hasattr(signal, 'SIGINT'):
        signal.signal(signal.SIGINT, signal_handler)

    try:
        # 连接到Telegram
        await client.start(phone=PHONE_NUMBER)
        logger.info("已连接到Telegram")

        # 获取当前用户信息
        me = await client.get_me()
        logger.info(f"登录账号: {me.first_name} (@{me.username})")

        # 解析并存储要监控的实体
        monitored_entities = []

        # 检查配置是否为空（文件不存在或channels和bots都为空）
        config_is_empty = (not config.get('channels') and not config.get('bots'))

        if config_is_empty:
            # 如果配置为空，获取所有channels和bots
            logger.info("配置为空，将监控所有channels和bots")
            print("配置为空，将监控所有channels和bots")

            # 获取所有对话
            dialogs = await client.get_dialogs()

            for dialog in dialogs:
                entity = dialog.entity
                if isinstance(entity, Channel):
                    if entity.broadcast:  # 是频道
                        monitored_entities.append(entity.id)
                        logger.info(f"将监控频道: {getattr(entity, 'title', None) or getattr(entity, 'username', '未知')} (ID: {entity.id})")
                elif isinstance(entity, User) and entity.bot:  # 是机器人
                    monitored_entities.append(entity.id)
                    logger.info(f"将监控机器人: {getattr(entity, 'first_name', None) or getattr(entity, 'username', '未知')} (ID: {entity.id})")
        else:
            # 解析频道
            channels = config.get('channels', []) or []
            for channel_id in channels:
                entity = await resolve_entity(client, channel_id)
                if entity:
                    monitored_entities.append(entity.id)
                    logger.info(f"将监控频道: {getattr(entity, 'title', None) or getattr(entity, 'username', '未知')} (ID: {entity.id})")

            # 解析机器人
            bots = config.get('bots', []) or []
            for bot_username in bots:
                entity = await resolve_entity(client, bot_username)
                if entity:
                    monitored_entities.append(entity.id)
                    logger.info(f"将监控机器人: {getattr(entity, 'first_name', None) or getattr(entity, 'username', '未知')} (ID: {entity.id})")

        # 如果没有指定任何实体，提示用户
        if not monitored_entities:
            logger.warning("未指定任何要监控的实体，请在配置文件中添加频道、群组或机器人")
            print("未指定任何要监控的实体，请在配置文件中添加频道、群组或机器人")

        # 如果配置了获取历史消息，则获取每个实体的历史消息
        if FETCH_HISTORY_COUNT > 0 and monitored_entities:
            logger.info(f"正在获取每个实体的最新 {FETCH_HISTORY_COUNT} 条历史消息...")
            print(f"正在获取每个实体的最新 {FETCH_HISTORY_COUNT} 条历史消息...")

            # 定义处理单个历史消息的异步函数
            async def process_history_message(client, message, entity_name):
                try:
                    # 保存消息ID，确保在处理过程中不会丢失
                    message_id = None
                    if hasattr(message, 'id'):
                        message_id = message.id
                        logger.info(f"处理历史消息 ID: {message_id} (实体: {entity_name})")

                    # 创建一个包含原始ID的消息对象
                    if message_id is not None:
                        # 创建一个字典，用于传递额外信息
                        extra_info = {'original_message_id': message_id}
                        # 处理历史消息，传递额外信息并等待处理完成
                        await process_message(client, message, is_history=True, **extra_info)
                    else:
                        # 如果没有ID，正常处理并等待完成
                        await process_message(client, message, is_history=True)

                    logger.info(f"历史消息处理完成: {message_id} (实体: {entity_name})")
                    return True
                except Exception as e:
                    logger.error(f"处理历史消息时出错: {e}", exc_info=True)
                    return False

            # 使用异步任务组处理不同实体的历史消息
            # 每个实体最多使用5个并发任务

            async def process_all_entities():
                for entity_id in monitored_entities:
                    try:
                        # 获取实体
                        entity = await client.get_entity(entity_id)
                        entity_name = getattr(entity, 'title', None) or getattr(entity, 'username', None) or getattr(entity, 'first_name', None) or str(entity_id)

                        logger.info(f"正在获取 {entity_name} 的历史消息...")

                        # 获取历史消息，使用更详细的参数
                        try:
                            # 尝试获取完整的消息对象，包括所有属性
                            messages = await client.get_messages(
                                entity,
                                limit=FETCH_HISTORY_COUNT,
                                # 添加额外参数，确保获取完整消息
                                ids=None,  # 不限制特定ID
                                reverse=False,  # 从最新的消息开始
                                add_offset=0,  # 不添加偏移
                                search=None,  # 不搜索特定内容
                                filter=None,  # 不过滤特定类型
                                from_user=None,  # 不限制特定用户
                                wait_time=None  # 使用默认等待时间
                            )

                            if messages:
                                logger.info(f"找到 {len(messages)} 条历史消息 (实体: {entity_name})")

                                # 使用任务组并发处理每个消息
                                tasks = []
                                for message in messages:
                                    # 创建任务但限制并发数
                                    if len(tasks) >= 5:  # 每个实体最多5个并发任务
                                        # 等待一个任务完成
                                        _, pending = await asyncio.wait(
                                            tasks, return_when=asyncio.FIRST_COMPLETED
                                        )
                                        # 移除已完成的任务
                                        tasks = list(pending)

                                    # 添加新任务
                                    task = asyncio.create_task(
                                        process_history_message(client, message, entity_name)
                                    )
                                    add_task(task)  # 添加到任务跟踪器
                                    tasks.append(task)

                                # 等待剩余的任务完成
                                if tasks:
                                    await asyncio.gather(*tasks)

                                logger.info(f"实体 {entity_name} 的所有历史消息处理完成")
                            else:
                                logger.info(f"未找到历史消息 (实体: {entity_name})")
                        except Exception as e:
                            logger.error(f"获取历史消息时出错: {e}", exc_info=True)
                    except Exception as e:
                        logger.error(f"获取实体 {entity_id} 的历史消息时出错: {e}", exc_info=True)

            # 执行所有实体的历史消息处理
            await process_all_entities()

        # 监控指定实体的新消息
        @client.on(events.NewMessage)
        async def handle_new_message(event):
            try:
                # 获取消息来源
                chat = await event.get_chat()

                # 检查是否是我们要监控的实体
                if not monitored_entities or chat.id in monitored_entities:
                    # 检查是否正在关闭
                    if shutdown_event.is_set():
                        logger.debug("程序正在关闭，跳过新消息处理")
                        return

                    # 创建异步任务并添加到任务跟踪器
                    task = asyncio.create_task(process_message(client, event))
                    add_task(task)
                    logger.debug(f"已创建消息处理任务: {chat.id}")
            except Exception as e:
                logger.error(f"处理新消息时出错: {e}", exc_info=True)

        # 确保事件处理器被使用
        logger.info(f"已注册新消息处理器: {handle_new_message.__name__}")

        # 保持客户端运行
        logger.info("监控已启动，等待消息...")
        print("监控已启动，等待消息...")

        # 运行直到断开连接或收到关闭信号
        try:
            await client.run_until_disconnected()
        except KeyboardInterrupt:
            logger.info("接收到键盘中断信号")
        except Exception as e:
            logger.error(f"客户端运行时出错: {e}")

    except Exception as e:
        logger.error(f"程序初始化或运行时出错: {e}", exc_info=True)
    finally:
        # 确保优雅关闭
        await graceful_shutdown(client)

if __name__ == '__main__':
    try:
        # 正常运行监控程序
        asyncio.run(main())
    except KeyboardInterrupt:
        print("程序已手动停止")
    except Exception as e:
        logger.error(f"程序运行出错: {e}", exc_info=True)
        print(f"程序运行出错: {e}")



