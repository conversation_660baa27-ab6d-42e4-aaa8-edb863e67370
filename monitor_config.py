# 通过运行此文件获取telegram账号已经关注的频道和机器人信息，并生成monitor_config.yaml文件

import asyncio
import os
import logging
from telethon import TelegramClient
from telethon.tl.types import Channel, User, PeerChannel, PeerChat, PeerUser
from telethon.tl.functions.channels import GetFullChannelRequest
from telethon.tl.functions.users import GetFullUserRequest
from dotenv import load_dotenv
import yaml
from datetime import datetime

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.WARNING  # 将日志级别改为WARNING
)
logger = logging.getLogger(__name__)

# 禁用Telethon的日志
telethon_logger = logging.getLogger('telethon')
telethon_logger.setLevel(logging.CRITICAL)  # 只显示严重错误

# 加载环境变量
load_dotenv()

# API凭据配置
API_ID = int(os.getenv('API_ID'))
API_HASH = os.getenv('API_HASH')
PHONE_NUMBER = os.getenv('PHONE_NUMBER')
SESSION_NAME = 'monitor_session'

# YAML配置文件路径
CONFIG_FILE = 'monitor_config.yaml'

# 默认配置
DEFAULT_CONFIG = {
    'channels': [],  # 频道用户名或ID列表
    'bots': []       # 机器人用户名列表
}

async def resolve_entity(client, entity_id):
    """解析实体ID或用户名为Telegram实体对象"""
    try:
        # 检查是否是数字ID
        if isinstance(entity_id, int) or (isinstance(entity_id, str) and entity_id.isdigit()):
            # 转换为整数ID
            entity_id = int(entity_id)

            try:
                # 尝试作为频道ID
                entity = await client.get_entity(PeerChannel(entity_id))

                # 如果是频道但没有用户名，尝试获取完整信息
                if isinstance(entity, Channel) and not entity.username:
                    try:
                        full_channel = await client(GetFullChannelRequest(entity))
                        if hasattr(full_channel, 'chats') and full_channel.chats:
                            for chat in full_channel.chats:
                                if chat.id == entity.id and hasattr(chat, 'username') and chat.username:
                                    entity = chat
                                    break
                    except Exception as e:
                        logger.warning(f"获取频道完整信息失败: {e}")

                return entity
            except Exception:
                try:
                    # 尝试作为群组ID
                    return await client.get_entity(PeerChat(entity_id))
                except Exception:
                    try:
                        # 尝试作为用户ID
                        return await client.get_entity(PeerUser(entity_id))
                    except Exception as e:
                        logger.error(f"无法解析ID: {entity_id}，错误: {e}")
                        return None
        else:
            # 如果是用户名，确保没有@前缀
            clean_username = entity_id.lstrip('@')
            try:
                return await client.get_entity(clean_username)
            except Exception as e:
                logger.error(f"无法解析用户名: {clean_username}，错误: {e}")
                return None
    except Exception as e:
        logger.error(f"解析实体出错: {e}")
        return None

async def get_entity_details(client, entity_id):
    """获取实体的详细信息"""
    try:
        entity = await resolve_entity(client, entity_id)
        if not entity:
            return "", "", {}

        username = ""
        about = ""
        additional_info = {}

        # 获取完整的实体信息
        try:
            if isinstance(entity, Channel):
                full_entity = await client(GetFullChannelRequest(entity))
                additional_info = {
                    "成员数": getattr(full_entity.full_chat, 'participants_count', 'N/A'),
                    "描述": getattr(full_entity.full_chat, 'about', 'N/A'),
                    "是否公开": getattr(entity, 'username', None) is not None,
                    "是否广播频道": getattr(entity, 'broadcast', False),
                    "创建时间": datetime.fromtimestamp(getattr(entity, 'date', datetime.now()).timestamp()).strftime('%Y-%m-%d %H:%M:%S')
                }
            elif isinstance(entity, User) and entity.bot:
                full_entity = await client(GetFullUserRequest(entity))
                additional_info = {
                    "机器人信息": getattr(full_entity.full_user, 'about', 'N/A'),
                    "是否验证": getattr(entity, 'verified', False),
                    "创建时间": datetime.fromtimestamp(getattr(entity, 'date', datetime.now()).timestamp()).strftime('%Y-%m-%d %H:%M:%S')
                }

        except Exception as e:
            logger.warning(f"获取完整实体信息失败: {e}")

        # 获取用户名或ID链接
        if hasattr(entity, 'username') and entity.username:
            username = f"t.me/{entity.username}"
        elif hasattr(entity, 'id'):
            chat_id = str(entity.id)
            if chat_id.startswith('-100'):
                chat_id = chat_id[4:]
            username = f"t.me/c/{chat_id}"

        # 获取简介/描述
        if hasattr(entity, 'about') and entity.about:
            about = entity.about.split('\n')[0][:50] + ('...' if len(entity.about) > 50 else '')

        return username, about, additional_info
    except Exception as e:
        logger.error(f"获取实体详情出错: {e}")
        return "", "", {}

async def verify_account(client):
    """验证账户并获取完整的账户信息"""
    try:
        # 获取当前用户信息
        me = await client.get_me()
        full_user = await client(GetFullUserRequest(me))

        # 收集账户信息
        account_info = {
            "用户ID": me.id,
            "用户名": f"@{me.username}" if me.username else "无",
            "名字": me.first_name,
            "姓氏": me.last_name if me.last_name else "无",
            "手机号": me.phone if me.phone else "无",
            "是否验证": getattr(me, 'verified', False),
            "是否高级用户": getattr(me, 'premium', False),
            "语言代码": getattr(me, 'lang_code', "未知"),
            "个人简介": getattr(full_user.full_user, 'about', "无")
        }

        # 尝试获取注册时间，如果不可用则忽略
        if hasattr(me, 'date'):
            try:
                account_info["注册时间"] = datetime.fromtimestamp(me.date.timestamp()).strftime('%Y-%m-%d %H:%M:%S')
            except (AttributeError, TypeError):
                account_info["注册时间"] = "未知"
        else:
            account_info["注册时间"] = "未知"

        print("\n=== 账户信息 ===")
        for key, value in account_info.items():
            print(f"{key}: {value}")
        print("=" * 50 + "\n")

        return True
    except Exception as e:
        logger.error(f"验证账户时出错: {e}")
        return False

async def list_dialogs():
    """列出所有对话并生成配置文件"""
    try:
        # 初始化客户端
        client = TelegramClient(SESSION_NAME, API_ID, API_HASH)
        await client.start(phone=PHONE_NUMBER)

        # 验证账户
        if not await verify_account(client):
            print("账户验证失败，请检查凭据")
            return

        print("# 以下是当前账号可用的所有频道和机器人列表，可直接复制到 monitor_config.yaml\n")

        # 获取所有对话
        dialogs = await client.get_dialogs()

        # 分类显示
        channels = []
        bots = []

        for dialog in dialogs:
            entity = dialog.entity
            if isinstance(entity, Channel):
                if entity.broadcast:
                    channels.append((entity.id, getattr(entity, 'title', None) or getattr(entity, 'username', '未知')))
            elif isinstance(entity, User):
                if entity.bot:
                    bots.append((entity.id, getattr(entity, 'username', None) or entity.first_name))

        # 打印频道信息
        if channels:
            print("# 要监控的频道列表")
            print("channels:")
            for id, name in channels:
                try:
                    username, about, additional_info = await get_entity_details(client, id)
                    
                    # 构建基本输出
                    if username and "t.me/" in username and "t.me/c/" not in username:
                        username_part = username.split("t.me/")[1]
                        copy_output = f"  - {username_part} # {id}___{name}___t.me/{username_part}"
                    else:
                        chat_id = str(id)
                        if chat_id.startswith('-100'):
                            chat_id = chat_id[4:]
                        copy_output = f"  - {id} # {id}___{name}___t.me/c/{chat_id}"

                    # 添加额外信息
                    if additional_info:
                        members = additional_info.get("成员数", "N/A")
                        is_public = "公开" if additional_info.get("是否公开", False) else "私密"
                        copy_output += f"___{is_public}___成员:{members}"

                    print(copy_output)
                except Exception as e:
                    logger.error(f"处理频道信息时出错 (ID: {id}): {e}")
                    continue
            print("")

        # 打印机器人信息
        if bots:
            print("# 要监控的机器人列表")
            print("bots:")
            for id, name in bots:
                try:
                    username, about, additional_info = await get_entity_details(client, id)
                    
                    # 构建基本输出
                    if username and "t.me/" in username and "t.me/c/" not in username:
                        username_part = username.split("t.me/")[1]
                        # 安全处理机器人信息
                        bot_info = additional_info.get("机器人信息", "")
                        if bot_info:
                            bot_info = bot_info.replace("\n", " ")[:50]
                        else:
                            bot_info = name or "未知"
                        copy_output = f"  - {username_part} # {id}___{bot_info}___t.me/{username_part}"
                    else:
                        copy_output = f"  - {id} # {id}___{name or '未知'}"

                    # 添加验证状态
                    if additional_info.get("是否验证", False):
                        copy_output += "___已验证"
                    else:
                        copy_output += "___未验证"

                    print(copy_output)
                except Exception as e:
                    logger.error(f"处理机器人信息时出错 (ID: {id}): {e}")
                    continue
            print("")

        # 生成默认配置文件（如果不存在）
        if not os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'w', encoding='utf-8') as file:
                yaml.dump(DEFAULT_CONFIG, file, default_flow_style=False, allow_unicode=True)
            print(f"# 已创建默认配置文件: {CONFIG_FILE}")
        else:
            print("# 提示：将需要监控的频道和机器人从上面的列表复制到 monitor_config.yaml 文件中")

        await client.disconnect()
    except Exception as e:
        logger.error(f"列出对话时出错: {e}")
        print(f"发生错误: {e}")
        await client.disconnect()

if __name__ == '__main__':
    try:
        print("正在连接到 Telegram...")
        asyncio.run(list_dialogs())
    except KeyboardInterrupt:
        print("\n程序已手动停止")
    except Exception as e:
        print(f"程序运行出错: {e}") 



