#  简单的pyproject配置, 使用 PEP 621 标准，不依赖于 Poetry。
#  创建并激活虚拟环境：
#  Windows: uv venv && .venv\Scripts\activate && uv pip install -e .
#  Linux: uv venv && source .venv/bin/activate && uv pip install -e .

[project]
name = "py_tg_telethon_supabase"
version = "8.8.8"
requires-python = ">=3.11,<3.13"
dependencies = [
    "telethon>=1.40.0",
    "python-dotenv>=1.1.0",
    "pyyaml>=6.0.2",
    "requests>=2.32.4",
    "numpy>=2.3.0",
    "pandas>=2.3.0",
    "Pillow>=11.2.1",
    "python-dateutil>=2.9.0.post0",
    "PyWavelets>=1.8.0",
    "matplotlib>=3.10.3",
    "openai>=1.88.0",
    "backoff>=2.2.1",
    "httpx>=0.28.1",
    "tenacity>=9.1.2",
    "supabase>=2.15.3"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
# 包含顶级Python模块
packages = ["."]


