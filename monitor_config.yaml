# Telegram监控配置文件可以使用以下方式指定要监控的实体:
# - 用户名: t.me后面的部分,优先使用这种方式,更加直观且易于管理,但是有些频道和机器人没有公开或者没有用户名
# - ID: 都有一个唯一的数字ID,在频道机器人群组没有公开用户名时使用
# 查看所有可用的频道和机器人,⭐️运行 python monitor_config.py

# 要监控的频道列表
channels:
  - nwsmkr # 1461988268___Newsmaker.club___t.me/nwsmkr___公开___成员:18783
  - infinityhedge # 1380328653___infinityhedge___t.me/infinityhedge___公开___成员:32812
  - AGGRNEWSWIRE # 2241189018___aggrnews___t.me/AGGRNEWSWIRE___公开___成员:4092
  - 1279597711 # 1279597711___方程式新闻 BWEnews___t.me/c/1279597711___私密___成员:59498
  - TreeNewsFeed # 1219306781___Tree News___t.me/TreeNewsFeed___公开___成员:29323
  - telonews_cn # 1525379130___Telo News 简体中文 - 加密货币｜DeFi ｜Web3___t.me/telonews_cn___公开___成员:133382
  - chengzi95330 # 1655856391___橙子的禁言群🈲❌🈲___t.me/chengzi95330___公开___成员:3987
  - foresightnews # 1526765830___Foresight News___t.me/foresightnews___公开___成员:18069
  - theblockbeats # 1387109317___BlockBeats___t.me/theblockbeats___公开___成员:29536
  - news6551 # 2395608815___6551News___t.me/news6551___公开___成员:2645
  - xhqcankao # 1472283197___风向旗参考快讯___t.me/xhqcankao___公开___成员:117137
  - MEXC_New_Listings # 1510597645___MEXC New Listings___t.me/MEXC_New_Listings___公开___成员:484
  - 1462219382 # 1462219382___Crypto Headlines___t.me/c/1462219382___私密___成员:130379
  - cryptocurrency_media # 1488075213___CRYPTO NEWS___t.me/cryptocurrency_media___公开___成员:45487
  - 1769001896 # 1769001896___Coingraph | News___t.me/c/1769001896___私密___成员:1057362
  - 1125107539 # 1125107539___科技圈🎗在花频道📮___t.me/c/1125107539___私密___成员:171271
  - wublock # 1317044897___吴说区块链 新闻与深度___t.me/wublock___公开___成员:14979
  - cointelegraph # 1072723547___Cointelegraph___t.me/cointelegraph___公开___成员:446058
  - SolidIntelX # 2457358877___Solid Intel 📡___t.me/SolidIntelX___公开___成员:5199
  - unfolded # 1358788312___unfolded.___t.me/unfolded___公开___成员:110016
  - cryptoquant_official # 1343715577___CryptoQuant___t.me/cryptoquant_official___公开___成员:61306
  - OKX_New_Listings # 2121971725___OKX New Listings___t.me/OKX_New_Listings___公开___成员:456
  - NewListingsFeed # 2043434663___New Listings Feed___t.me/NewListingsFeed___公开___成员:25106
  - OnchainLens # 2203893277___Onchain Lens___t.me/OnchainLens___公开___成员:2191
  - Bybit_Announcements # 1449478440___Bybit Announcements___t.me/Bybit_Announcements___公开___成员:558880
  - lookonchainchannel # 2207386483___Lookonchain___t.me/lookonchainchannel___公开___成员:14756
  - WatcherGuru # 1556054753___Watcher Guru___t.me/WatcherGuru___公开___成员:526627
  - TrumpTruthSocial_Alert # 2423576123___Trump Truth Social Alert___t.me/TrumpTruthSocial_Alert___公开___成员:2302
  - uswhnews # 2652399733___美国白宫官方新闻（中文）___t.me/uswhnews___公开___成员:77
  - unfolded_defi # 1569666929___unfolded. DeFi___t.me/unfolded_defi___公开___成员:20915
  - ZoomerfiedNews # 1651524056___Zoomer News___t.me/ZoomerfiedNews___公开___成员:15515
  - Binance_New_Listing_Delisting # 1615940886___Moonance New Listings___t.me/Binance_New_Listing_Delisting___公开___成员:987
  - Bybit_New_Listings # 2024533109___Bybit New Listings___t.me/Bybit_New_Listings___公开___成员:795
  - BybitListingAlerts # 1535652205___Bybit Listing___t.me/BybitListingAlerts___公开___成员:1048
  - binance_announcements # 1146915409___Binance Announcements___t.me/binance_announcements___公开___成员:5425732
  - binance_cn # 1075793644___币安公告___t.me/binance_cn___公开___成员:39419
  - hyperliquid_api # 1745127781___Hyperliquid API Announcements___t.me/hyperliquid_api___公开___成员:1184
  - spotonchain # 1723367507___SpotOnChain | Announcement___t.me/spotonchain___公开___成员:32404
  - layergg # 1497020843___Layergg exchange & project alert___t.me/layergg___公开___成员:23339
  - hyperliquid_announcements # 1736253760___Hyperliquid Announcements___t.me/hyperliquid_announcements___公开___成员:14724

# 要监控的机器人列表
bots:
  - yingheTWbot # 7039829949___中文：@yingheTWbot English：@yingheTWenbot 推特消息订阅实时监控 ___t.me/yingheTWbot___未验证


